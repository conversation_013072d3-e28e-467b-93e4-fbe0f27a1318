/**
 * 深度学习节点 - 第三部分
 * 完成批次3.3的深度学习节点（7-15）
 */

import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { DeepLearningNode } from './DeepLearningNodes';

/**
 * 7. 变分自编码器节点
 */
export class VAEModelNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/vaeModel';
  public static readonly NAME = '变分自编码器';
  public static readonly DESCRIPTION = 'VAE变分自编码器实现';

  constructor() {
    super(VAEModelNode.TYPE, VAEModelNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('inputData', 'array', '输入数据', []);
    this.addInput('latentDim', 'number', '潜在维度', 20);
    this.addInput('encoderLayers', 'array', '编码器层', [784, 400, 40]);
    this.addInput('decoderLayers', 'array', '解码器层', [20, 400, 784]);
  }

  private setupOutputs(): void {
    this.addOutput('reconstructed', 'array', '重构数据');
    this.addOutput('latentMean', 'array', '潜在均值');
    this.addOutput('latentLogVar', 'array', '潜在对数方差');
    this.addOutput('klLoss', 'number', 'KL散度损失');
    this.addOutput('reconstructionLoss', 'number', '重构损失');
  }

  public execute(inputs: any): any {
    try {
      const inputData = this.getInputValue(inputs, 'inputData');
      const latentDim = this.getInputValue(inputs, 'latentDim');
      const encoderLayers = this.getInputValue(inputs, 'encoderLayers');
      const decoderLayers = this.getInputValue(inputs, 'decoderLayers');

      if (!Array.isArray(inputData) || inputData.length === 0) {
        throw new Error('输入数据无效');
      }

      // 编码器
      const encoded = this.encoder(inputData, encoderLayers, latentDim);
      
      // 重参数化技巧
      const sampled = this.reparameterize(encoded.mean, encoded.logVar);
      
      // 解码器
      const reconstructed = this.decoder(sampled, decoderLayers);
      
      // 计算损失
      const klLoss = this.computeKLLoss(encoded.mean, encoded.logVar);
      const reconstructionLoss = this.computeReconstructionLoss(inputData, reconstructed);

      return {
        reconstructed,
        latentMean: encoded.mean,
        latentLogVar: encoded.logVar,
        klLoss,
        reconstructionLoss,
        result: { 
          status: 'computed', 
          latentDimension: latentDim,
          totalLoss: klLoss + reconstructionLoss
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        reconstructed: [],
        latentMean: [],
        latentLogVar: [],
        klLoss: 0,
        reconstructionLoss: 0,
        result: null,
        success: false,
        error: error instanceof Error ? error.message : 'VAE计算失败'
      };
    }
  }

  private encoder(input: number[], layers: number[], latentDim: number): any {
    let output = [...input];
    
    // 编码器前向传播
    for (let i = 1; i < layers.length; i++) {
      output = this.denseLayer(output, layers[i]);
    }
    
    // 分离均值和对数方差
    const mean = output.slice(0, latentDim);
    const logVar = output.slice(latentDim, latentDim * 2);
    
    return { mean, logVar };
  }

  private decoder(latent: number[], layers: number[]): number[] {
    let output = [...latent];
    
    // 解码器前向传播
    for (let i = 1; i < layers.length; i++) {
      const isLastLayer = i === layers.length - 1;
      output = this.denseLayer(output, layers[i], isLastLayer ? 'sigmoid' : 'relu');
    }
    
    return output;
  }

  private reparameterize(mean: number[], logVar: number[]): number[] {
    const sampled: number[] = [];
    
    for (let i = 0; i < mean.length; i++) {
      const std = Math.exp(0.5 * logVar[i]);
      const eps = this.randomNormal(); // 标准正态分布采样
      sampled[i] = mean[i] + std * eps;
    }
    
    return sampled;
  }

  private denseLayer(input: number[], outputSize: number, activation: string = 'relu'): number[] {
    const output: number[] = Array(outputSize).fill(0);
    
    for (let i = 0; i < outputSize; i++) {
      let sum = 0;
      for (let j = 0; j < input.length; j++) {
        sum += input[j] * (Math.random() * 0.2 - 0.1); // 随机权重
      }
      
      // 应用激活函数
      switch (activation) {
        case 'relu':
          output[i] = Math.max(0, sum);
          break;
        case 'sigmoid':
          output[i] = 1 / (1 + Math.exp(-sum));
          break;
        default:
          output[i] = sum;
      }
    }
    
    return output;
  }

  private computeKLLoss(mean: number[], logVar: number[]): number {
    let klLoss = 0;
    
    for (let i = 0; i < mean.length; i++) {
      klLoss += -0.5 * (1 + logVar[i] - mean[i] * mean[i] - Math.exp(logVar[i]));
    }
    
    return klLoss / mean.length;
  }

  private computeReconstructionLoss(original: number[], reconstructed: number[]): number {
    let loss = 0;
    
    for (let i = 0; i < original.length; i++) {
      const diff = original[i] - reconstructed[i];
      loss += diff * diff;
    }
    
    return loss / original.length;
  }

  private randomNormal(): number {
    // Box-Muller变换生成标准正态分布
    const u1 = Math.random();
    const u2 = Math.random();
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }
}

/**
 * 8. 注意力机制节点
 */
export class AttentionMechanismNode extends DeepLearningNode {
  public static readonly TYPE = 'ai/attentionMechanism';
  public static readonly NAME = '注意力机制';
  public static readonly DESCRIPTION = '注意力机制实现';

  constructor() {
    super(AttentionMechanismNode.TYPE, AttentionMechanismNode.NAME);
    this.setupInputs();
    this.setupOutputs();
  }

  private setupInputs(): void {
    this.addInput('query', 'array', '查询向量', []);
    this.addInput('key', 'array', '键向量', []);
    this.addInput('value', 'array', '值向量', []);
    this.addInput('attentionType', 'string', '注意力类型', 'scaled_dot_product');
    this.addInput('temperature', 'number', '温度参数', 1.0);
  }

  private setupOutputs(): void {
    this.addOutput('attentionOutput', 'array', '注意力输出');
    this.addOutput('attentionWeights', 'array', '注意力权重');
    this.addOutput('alignmentScores', 'array', '对齐分数');
  }

  public execute(inputs: any): any {
    try {
      const query = this.getInputValue(inputs, 'query');
      const key = this.getInputValue(inputs, 'key');
      const value = this.getInputValue(inputs, 'value');
      const attentionType = this.getInputValue(inputs, 'attentionType');
      const temperature = this.getInputValue(inputs, 'temperature');

      if (!Array.isArray(query) || !Array.isArray(key) || !Array.isArray(value)) {
        throw new Error('输入向量无效');
      }

      // 计算注意力
      const result = this.computeAttention(query, key, value, attentionType, temperature);

      return {
        attentionOutput: result.output,
        attentionWeights: result.weights,
        alignmentScores: result.scores,
        result: { 
          status: 'computed', 
          attentionType,
          outputDimension: result.output.length
        },
        success: true,
        error: ''
      };

    } catch (error) {
      return {
        attentionOutput: [],
        attentionWeights: [],
        alignmentScores: [],
        result: null,
        success: false,
        error: error instanceof Error ? error.message : '注意力机制计算失败'
      };
    }
  }

  private computeAttention(query: number[], key: number[][], value: number[][], type: string, temperature: number): any {
    switch (type) {
      case 'scaled_dot_product':
        return this.scaledDotProductAttention(query, key, value, temperature);
      case 'additive':
        return this.additiveAttention(query, key, value);
      case 'multiplicative':
        return this.multiplicativeAttention(query, key, value);
      default:
        return this.scaledDotProductAttention(query, key, value, temperature);
    }
  }

  private scaledDotProductAttention(query: number[], key: number[][], value: number[][], temperature: number): any {
    const scores: number[] = [];
    
    // 计算注意力分数
    for (let i = 0; i < key.length; i++) {
      let score = 0;
      for (let j = 0; j < Math.min(query.length, key[i].length); j++) {
        score += query[j] * key[i][j];
      }
      scores.push(score / (Math.sqrt(query.length) * temperature));
    }
    
    // Softmax归一化
    const weights = this.softmax(scores);
    
    // 计算加权输出
    const output: number[] = Array(value[0].length).fill(0);
    for (let i = 0; i < value.length; i++) {
      for (let j = 0; j < value[i].length; j++) {
        output[j] += weights[i] * value[i][j];
      }
    }
    
    return { output, weights, scores };
  }

  private additiveAttention(query: number[], key: number[][], value: number[][]): any {
    const scores: number[] = [];
    
    // 加性注意力计算
    for (let i = 0; i < key.length; i++) {
      let score = 0;
      for (let j = 0; j < Math.min(query.length, key[i].length); j++) {
        score += Math.tanh(query[j] + key[i][j]); // 简化的加性注意力
      }
      scores.push(score);
    }
    
    const weights = this.softmax(scores);
    
    const output: number[] = Array(value[0].length).fill(0);
    for (let i = 0; i < value.length; i++) {
      for (let j = 0; j < value[i].length; j++) {
        output[j] += weights[i] * value[i][j];
      }
    }
    
    return { output, weights, scores };
  }

  private multiplicativeAttention(query: number[], key: number[][], value: number[][]): any {
    const scores: number[] = [];
    
    // 乘性注意力计算
    for (let i = 0; i < key.length; i++) {
      let score = 0;
      for (let j = 0; j < Math.min(query.length, key[i].length); j++) {
        score += query[j] * key[i][j];
      }
      scores.push(score);
    }
    
    const weights = this.softmax(scores);
    
    const output: number[] = Array(value[0].length).fill(0);
    for (let i = 0; i < value.length; i++) {
      for (let j = 0; j < value[i].length; j++) {
        output[j] += weights[i] * value[i][j];
      }
    }
    
    return { output, weights, scores };
  }

  private softmax(scores: number[]): number[] {
    const maxScore = Math.max(...scores);
    const expScores = scores.map(score => Math.exp(score - maxScore));
    const sumExp = expScores.reduce((a, b) => a + b, 0);
    return expScores.map(exp => exp / sumExp);
  }
}
