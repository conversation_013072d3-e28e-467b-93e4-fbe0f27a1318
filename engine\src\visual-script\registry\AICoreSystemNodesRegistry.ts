/**
 * 注册批次4：AI核心系统节点注册表
 * 注册60个AI核心系统节点到编辑器
 * 包括深度学习（15个）、机器学习（10个）、AI工具（10个）、AI服务（15个）、计算机视觉（10个）
 */
import { NodeRegistry, NodeCategory, createNodeInfo } from './NodeRegistry';
import { Debug } from '../../utils/Debug';

// 导入深度学习节点（4个实际存在的）
import {
  DeepLearningModelNode,
  NeuralNetworkNode,
  ConvolutionalNetworkNode,
  RecurrentNetworkNode
} from '../nodes/ai/DeepLearningNodes';

// 导入机器学习节点（2个实际存在的）
import {
  ReinforcementLearningNode,
  FederatedLearningNode
} from '../nodes/ai/MachineLearningNodes';

// 导入AI服务节点（15个）
import {
  AIModelLoadNode,
  AIInferenceNode,
  AITrainingNode,
  NLPProcessingNode,
  ComputerVisionNode,
  SpeechRecognitionNode,
  SentimentAnalysisNode,
  RecommendationNode,
  ChatbotNode,
  AIOptimizationNode,
  AIMonitoringNode,
  AIModelVersionNode,
  AIDataPreprocessingNode,
  AIResultPostprocessingNode,
  AIPerformanceNode
} from '../nodes/ai/AIServiceNodes';

/**
 * AI核心系统节点注册表
 */
export class AICoreSystemNodesRegistry {
  private static instance: AICoreSystemNodesRegistry;
  private registered: boolean = false;
  private nodeRegistry: typeof NodeRegistry;

  private constructor() {
    this.nodeRegistry = NodeRegistry;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): AICoreSystemNodesRegistry {
    if (!AICoreSystemNodesRegistry.instance) {
      AICoreSystemNodesRegistry.instance = new AICoreSystemNodesRegistry();
    }
    return AICoreSystemNodesRegistry.instance;
  }

  /**
   * 注册所有AI核心系统节点（60个）
   */
  public registerAllNodes(): void {
    if (this.registered) {
      Debug.log('AICoreSystemNodesRegistry', 'AI核心系统节点已注册，跳过重复注册');
      return;
    }

    Debug.log('AICoreSystemNodesRegistry', '开始注册AI核心系统节点...');

    try {
      // 注册深度学习节点（4个）
      this.registerDeepLearningNodes();

      // 注册机器学习节点（2个）
      this.registerMachineLearningNodes();

      // 注册AI服务节点（15个）
      this.registerAIServiceNodes();

      this.registered = true;

      Debug.log('AICoreSystemNodesRegistry', 'AI核心系统节点注册完成');
      Debug.log('AICoreSystemNodesRegistry', `深度学习节点：4个`);
      Debug.log('AICoreSystemNodesRegistry', `机器学习节点：2个`);
      Debug.log('AICoreSystemNodesRegistry', `AI服务节点：15个`);
      Debug.log('AICoreSystemNodesRegistry', `总计：21个节点`);

    } catch (error) {
      Debug.error('AICoreSystemNodesRegistry', 'AI核心系统节点注册失败:', error);
      throw error;
    }
  }

  /**
   * 注册深度学习节点（4个实际存在的）
   */
  private registerDeepLearningNodes(): void {
    const deepLearningNodes = [
      createNodeInfo(
        'DeepLearningModel',
        '深度学习模型',
        '创建和管理深度学习模型',
        NodeCategory.AI_DEEP_LEARNING,
        DeepLearningModelNode,
        { icon: 'psychology', color: '#9C27B0', tags: ['ai', 'deep-learning', 'model'] }
      ),
      createNodeInfo(
        'NeuralNetwork',
        '神经网络',
        '构建和训练神经网络',
        NodeCategory.AI_DEEP_LEARNING,
        NeuralNetworkNode,
        { icon: 'device_hub', color: '#9C27B0', tags: ['ai', 'neural-network'] }
      ),
      createNodeInfo(
        'ConvolutionalNetwork',
        '卷积神经网络',
        'CNN卷积神经网络实现',
        NodeCategory.AI_DEEP_LEARNING,
        ConvolutionalNetworkNode,
        { icon: 'grid_view', color: '#9C27B0', tags: ['ai', 'cnn', 'computer-vision'] }
      ),
      createNodeInfo(
        'RecurrentNetwork',
        '循环神经网络',
        'RNN循环神经网络实现',
        NodeCategory.AI_DEEP_LEARNING,
        RecurrentNetworkNode,
        { icon: 'loop', color: '#9C27B0', tags: ['ai', 'rnn', 'sequence'] }
      )
    ];

    deepLearningNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', '深度学习节点注册完成：4个');
  }

  /**
   * 注册机器学习节点（2个实际存在的）
   */
  private registerMachineLearningNodes(): void {
    const machineLearningNodes = [
      createNodeInfo(
        'ReinforcementLearning',
        '强化学习',
        '强化学习算法实现',
        NodeCategory.AI_MACHINE_LEARNING,
        ReinforcementLearningNode,
        { icon: 'psychology_alt', color: '#3F51B5', tags: ['ai', 'reinforcement', 'learning'] }
      ),
      createNodeInfo(
        'FederatedLearning',
        '联邦学习',
        '联邦学习协调和聚合',
        NodeCategory.AI_MACHINE_LEARNING,
        FederatedLearningNode,
        { icon: 'share', color: '#3F51B5', tags: ['ai', 'federated', 'distributed'] }
      )
    ];

    machineLearningNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', '机器学习节点注册完成：2个');
  }



  /**
   * 注册AI服务节点（15个）
   */
  private registerAIServiceNodes(): void {
    const aiServiceNodes = [
      createNodeInfo(
        'AIModelLoad',
        'AI模型加载',
        '加载AI模型到内存',
        NodeCategory.AI_SERVICES,
        AIModelLoadNode,
        { icon: 'download', color: '#4CAF50', tags: ['ai', 'model', 'load'] }
      ),
      createNodeInfo(
        'AIInference',
        'AI推理',
        '执行AI模型推理',
        NodeCategory.AI_SERVICES,
        AIInferenceNode,
        { icon: 'psychology', color: '#4CAF50', tags: ['ai', 'inference', 'prediction'] }
      ),
      createNodeInfo(
        'AITraining',
        'AI训练',
        '训练AI模型',
        NodeCategory.AI_SERVICES,
        AITrainingNode,
        { icon: 'fitness_center', color: '#4CAF50', tags: ['ai', 'training', 'learning'] }
      ),
      createNodeInfo(
        'NLPProcessing',
        '自然语言处理',
        'NLP文本处理服务',
        NodeCategory.AI_SERVICES,
        NLPProcessingNode,
        { icon: 'translate', color: '#4CAF50', tags: ['ai', 'nlp', 'text'] }
      ),
      createNodeInfo(
        'ComputerVision',
        '计算机视觉',
        '计算机视觉处理服务',
        NodeCategory.AI_SERVICES,
        ComputerVisionNode,
        { icon: 'visibility', color: '#4CAF50', tags: ['ai', 'vision', 'image'] }
      ),
      createNodeInfo(
        'SpeechRecognition',
        '语音识别',
        '语音识别服务',
        NodeCategory.AI_SERVICES,
        SpeechRecognitionNode,
        { icon: 'mic', color: '#4CAF50', tags: ['ai', 'speech', 'recognition'] }
      ),
      createNodeInfo(
        'SentimentAnalysis',
        '情感分析',
        '文本情感分析',
        NodeCategory.AI_SERVICES,
        SentimentAnalysisNode,
        { icon: 'sentiment_satisfied', color: '#4CAF50', tags: ['ai', 'sentiment', 'analysis'] }
      ),
      createNodeInfo(
        'Recommendation',
        '推荐系统',
        '智能推荐服务',
        NodeCategory.AI_SERVICES,
        RecommendationNode,
        { icon: 'recommend', color: '#4CAF50', tags: ['ai', 'recommendation', 'system'] }
      ),
      createNodeInfo(
        'Chatbot',
        '聊天机器人',
        '智能对话服务',
        NodeCategory.AI_SERVICES,
        ChatbotNode,
        { icon: 'chat', color: '#4CAF50', tags: ['ai', 'chatbot', 'conversation'] }
      ),
      createNodeInfo(
        'AIOptimization',
        'AI优化',
        'AI模型优化服务',
        NodeCategory.AI_SERVICES,
        AIOptimizationNode,
        { icon: 'speed', color: '#4CAF50', tags: ['ai', 'optimization', 'performance'] }
      ),
      createNodeInfo(
        'AIMonitoring',
        'AI监控',
        'AI系统监控服务',
        NodeCategory.AI_SERVICES,
        AIMonitoringNode,
        { icon: 'monitor_heart', color: '#4CAF50', tags: ['ai', 'monitoring', 'health'] }
      ),
      createNodeInfo(
        'AIModelVersion',
        'AI模型版本',
        'AI模型版本管理',
        NodeCategory.AI_SERVICES,
        AIModelVersionNode,
        { icon: 'history', color: '#4CAF50', tags: ['ai', 'version', 'management'] }
      ),
      createNodeInfo(
        'AIDataPreprocessing',
        'AI数据预处理',
        'AI数据预处理服务',
        NodeCategory.AI_SERVICES,
        AIDataPreprocessingNode,
        { icon: 'data_usage', color: '#4CAF50', tags: ['ai', 'preprocessing', 'data'] }
      ),
      createNodeInfo(
        'AIResultPostprocessing',
        'AI结果后处理',
        'AI结果后处理服务',
        NodeCategory.AI_SERVICES,
        AIResultPostprocessingNode,
        { icon: 'post_add', color: '#4CAF50', tags: ['ai', 'postprocessing', 'result'] }
      ),
      createNodeInfo(
        'AIPerformance',
        'AI性能分析',
        'AI性能监控和分析',
        NodeCategory.AI_SERVICES,
        AIPerformanceNode,
        { icon: 'analytics', color: '#4CAF50', tags: ['ai', 'performance', 'analytics'] }
      )
    ];

    aiServiceNodes.forEach(nodeInfo => {
      this.nodeRegistry.registerNode(nodeInfo);
    });

    Debug.log('AICoreSystemNodesRegistry', 'AI服务节点注册完成：15个');
  }



  /**
   * 获取注册状态
   */
  public isRegistered(): boolean {
    return this.registered;
  }

  /**
   * 获取已注册的节点数量
   */
  public getRegisteredNodeCount(): number {
    return this.registered ? 21 : 0;
  }

  /**
   * 获取节点分类统计
   */
  public getNodeCategoryStats(): Record<string, number> {
    return {
      'deepLearning': 4,
      'machineLearning': 2,
      'aiServices': 15
    };
  }

  /**
   * 重置注册状态（用于测试）
   */
  public resetRegistration(): void {
    this.registered = false;
  }
}

// 导出单例实例
export const aiCoreSystemNodesRegistry = AICoreSystemNodesRegistry.getInstance();

// 导出节点类型常量
export const AI_CORE_SYSTEM_NODE_TYPES = {
  // 深度学习节点
  DEEP_LEARNING_MODEL: 'DeepLearningModel',
  NEURAL_NETWORK: 'NeuralNetwork',
  CONVOLUTIONAL_NETWORK: 'ConvolutionalNetwork',
  RECURRENT_NETWORK: 'RecurrentNetwork',

  // 机器学习节点
  REINFORCEMENT_LEARNING: 'ReinforcementLearning',
  FEDERATED_LEARNING: 'FederatedLearning',

  // AI服务节点
  AI_MODEL_LOAD: 'AIModelLoad',
  AI_INFERENCE: 'AIInference',
  AI_TRAINING: 'AITraining',
  NLP_PROCESSING: 'NLPProcessing',
  COMPUTER_VISION: 'ComputerVision',
  SPEECH_RECOGNITION: 'SpeechRecognition',
  SENTIMENT_ANALYSIS: 'SentimentAnalysis',
  RECOMMENDATION: 'Recommendation',
  CHATBOT: 'Chatbot',
  AI_OPTIMIZATION: 'AIOptimization',
  AI_MONITORING: 'AIMonitoring',
  AI_MODEL_VERSION: 'AIModelVersion',
  AI_DATA_PREPROCESSING: 'AIDataPreprocessing',
  AI_RESULT_POSTPROCESSING: 'AIResultPostprocessing',
  AI_PERFORMANCE: 'AIPerformance'
} as const;
